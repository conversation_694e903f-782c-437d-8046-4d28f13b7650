import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { AppSettings } from './types';

interface AppSettingsState {
  settings: AppSettings;
  updateSettings: (settings: Partial<AppSettings>) => void;
  resetSettings: () => void;
  updateWindowSize: (width: number, height: number) => void;
  updateWindowPosition: (x: number, y: number) => void;
}

// 默认应用设置
const defaultSettings: AppSettings = {
  windowSize: {
    width: 1200,
    height: 800,
  },
  windowPosition: {
    x: -1, // -1 表示居中
    y: -1,
  },
  alwaysOnTop: false,
  minimizeToTray: true,
  startMinimized: false,
  autoStart: false,
};

/**
 * 应用设置状态管理
 * 管理窗口设置和应用行为配置
 */
export const useAppSettingsStore = create<AppSettingsState>()(
  persist(
    set => ({
      settings: defaultSettings,

      updateSettings: (newSettings: Partial<AppSettings>) => {
        set(state => ({
          settings: {
            ...state.settings,
            ...newSettings,
          },
        }));
      },

      resetSettings: () => {
        set({ settings: defaultSettings });
      },

      updateWindowSize: (width: number, height: number) => {
        set(state => ({
          settings: {
            ...state.settings,
            windowSize: { width, height },
          },
        }));
      },

      updateWindowPosition: (x: number, y: number) => {
        set(state => ({
          settings: {
            ...state.settings,
            windowPosition: { x, y },
          },
        }));
      },
    }),
    {
      name: 'app-settings-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
