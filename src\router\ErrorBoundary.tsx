import { Component, ErrorInfo, ReactNode } from 'react';
import { Link } from 'react-router-dom';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

/**
 * 通用错误边界组件
 * 捕获组件树中的 JavaScript 错误
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-8">
          <div className="text-center max-w-md">
            <div className="mb-8">
              <h1 className="text-4xl font-bold text-red-500 mb-4">应用错误</h1>
              <p className="text-lg text-gray-600 mb-6">应用程序遇到了意外错误，请稍后重试。</p>
            </div>

            <div className="mb-8">
              <div className="w-32 h-32 mx-auto bg-gradient-to-r from-red-400 to-orange-500 rounded-full flex items-center justify-center">
                <span className="text-4xl text-white">⚠️</span>
              </div>
            </div>

            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="text-left mb-6 p-4 bg-white rounded-lg shadow">
                <summary className="cursor-pointer font-medium text-gray-800 mb-2">
                  错误详情 (开发模式)
                </summary>
                <pre className="text-sm text-red-600 overflow-auto">{this.state.error.stack}</pre>
              </details>
            )}

            <div className="space-y-4">
              <Link
                to="/"
                className="btn-primary"
              >
                🏠 返回首页
              </Link>
              <button
                onClick={() => window.location.reload()}
                className="btn-secondary ml-4"
              >
                🔄 刷新页面
              </button>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
