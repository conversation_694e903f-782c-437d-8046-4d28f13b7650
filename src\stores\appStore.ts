import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { AppState } from './types';

interface AppStoreState extends AppState {
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentRoute: (route: string) => void;
  toggleSidebar: () => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  toggleFullscreen: () => void;
  setFullscreen: (fullscreen: boolean) => void;
  clearError: () => void;
}

/**
 * 应用主状态管理
 * 管理应用的全局状态，如加载状态、错误信息、路由等
 */
export const useAppStore = create<AppStoreState>()(
  subscribeWithSelector(set => ({
    // 初始状态
    isLoading: false,
    error: null,
    currentRoute: '/',
    sidebarCollapsed: false,
    fullscreen: false,

    // Actions
    setLoading: (loading: boolean) => {
      set({ isLoading: loading });
    },

    setError: (error: string | null) => {
      set({ error });
    },

    setCurrentRoute: (route: string) => {
      set({ currentRoute: route });
    },

    toggleSidebar: () => {
      set(state => ({ sidebarCollapsed: !state.sidebarCollapsed }));
    },

    setSidebarCollapsed: (collapsed: boolean) => {
      set({ sidebarCollapsed: collapsed });
    },

    toggleFullscreen: () => {
      set(state => ({ fullscreen: !state.fullscreen }));
    },

    setFullscreen: (fullscreen: boolean) => {
      set({ fullscreen });
    },

    clearError: () => {
      set({ error: null });
    },
  }))
);

// 订阅路由变化，自动更新当前路由
if (typeof window !== 'undefined') {
  useAppStore.subscribe(
    state => state.currentRoute,
    currentRoute => {
      console.log('路由已切换到:', currentRoute);
    }
  );
}
