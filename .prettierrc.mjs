export default {
  // 一行最多 100 字符
  printWidth: 100,
  // 行尾需要有分号
  semi: true,
  // 使用单引号
  singleQuote: true,
  vueIndentScriptAndStyle: false,
  // 使用默认的折行标准 preserve | never
  proseWrap: 'never',
  // jsx 使用单引号代替双引号
  jsxSingleQuote: false,
  // 末尾不需要逗号 <es5|none|all>
  trailingComma: 'es5',
  // 大括号内的首尾需要空格
  bracketSpacing: true,
  // jsx 标签的反尖括号需要换行
  // jsxBracketSameLine: false,
  // 箭头函数，只有一个参数的时候，也需要括号 <always|avoid>
  arrowParens: 'avoid',
  // 不需要写文件开头的 @prettier 用于逐步过渡大型项目中未被格式化的代码标识
  // requirePragma: false,
  // 根据显示样式决定 html 要不要折行 <css|strict|ignore>"
  htmlWhitespaceSensitivity: 'strict',
  // 独占一行
  singleAttributePerLine: true,
  // 换行符使用 lf 结尾  <lf|crlf|cr|auto>
  endOfLine: 'auto',
  // 关闭 tab 缩进
  useTabs: false,
  //使用 2个tab 缩进
  // tabWidth: 2,
  overrides: [
    {
      files: '.*rc',
      options: {
        parser: 'json',
      },
    },
  ],
};
