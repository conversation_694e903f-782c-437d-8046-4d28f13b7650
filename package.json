{"name": "tauri-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"bootstrap": "pnpm install", "dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/roboto": "^5.2.6", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.1.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@turf/turf": "^7.2.0", "autoprefixer": "^10.4.21", "cesium": "^1.131.0", "earthsdk3": "^3.2.0", "earthsdk3-assets": "^3.0.5", "earthsdk3-cesium": "^3.2.0", "lodash-es": "^4.17.21", "react-router-dom": "^7.6.3", "vite-plugin-cesium": "^1.2.23", "vite-plugin-externals": "^0.6.2", "vite-plugin-static-copy": "^1.0.1", "zustand": "^5.0.6"}, "devDependencies": {"@iconify-json/heroicons": "^1.2.2", "@iconify-json/mdi": "^1.2.3", "@tauri-apps/cli": "^2", "@types/lodash-es": "^4.17.12", "@types/node": "^24.0.3", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "@unocss/preset-icons": "^66.2.3", "@unocss/preset-wind": "^66.2.3", "@vitejs/plugin-react": "^4.4.1", "less": "^4.3.0", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "~5.6.2", "unocss": "^66.2.3", "vite": "^6.0.3"}, "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devUrl": "http://localhost:5173", "frontendDist": "../dist"}}