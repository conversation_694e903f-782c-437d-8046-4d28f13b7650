# 路径别名配置

项目已配置路径别名，可以使用更简洁的导入方式。

## 可用别名

| 别名 | 实际路径 | 用途 |
|------|----------|------|
| `@/*` | `src/*` | 根目录别名 |
| `@/components/*` | `src/components/*` | 组件目录 |
| `@/pages/*` | `src/pages/*` | 页面目录 |
| `@/layout/*` | `src/layout/*` | 布局组件 |
| `@/stores/*` | `src/stores/*` | 状态管理 |
| `@/router/*` | `src/router/*` | 路由配置 |
| `@/types/*` | `src/types/*` | 类型定义 |
| `@/styles/*` | `src/styles/*` | 样式文件 |
| `@/assets/*` | `src/assets/*` | 静态资源 |

## 使用示例

### 原来的导入方式
```typescript
import Dashboard from '../../../pages/dashboard/Dashboard';
import { useThemeStore } from '../../../stores/themeStore';
import Layout from '../layout/Layout';
```

### 使用别名后
```typescript
import Dashboard from '@/pages/dashboard/Dashboard';
import { useThemeStore } from '@/stores/themeStore';
import Layout from '@/layout/Layout';
```

## 配置文件

路径别名在以下文件中配置：

1. **TypeScript 配置** (`tsconfig.json`)
   - 提供编译时的路径解析
   - IDE 智能提示支持

2. **Vite 配置** (`vite.config.ts`)
   - 提供构建时的路径解析
   - 开发服务器支持

## 优势

- ✅ **简洁明了**: 避免复杂的相对路径
- ✅ **易于维护**: 移动文件时不需要更新导入路径
- ✅ **IDE 支持**: 完整的智能提示和自动完成
- ✅ **统一风格**: 所有导入使用相同的别名格式

## 注意事项

1. 别名只在项目内部有效，不影响第三方库的导入
2. 建议优先使用最具体的别名（如 `@/components/` 而不是 `@/`）
3. 在重构时，IDE 通常能自动更新使用别名的导入路径
