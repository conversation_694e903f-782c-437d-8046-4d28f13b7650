import { create } from 'zustand';
import { subscribeWithSelector } from 'zustand/middleware';
import { ESObjectsManager, ESTerrainLayer, ESVOptionCzm } from 'earthsdk3';
import { ESCesiumViewer } from 'earthsdk3-cesium';
import { forEach } from 'lodash-es';

// 单例objm
let _objm: ESObjectsManager | null = null;
const subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'];

const tdtToken_terrian = import.meta.env.VITE_TDT_TOKEN_PERSONAL;
const tdtToken = import.meta.env.VITE_TDT_TOKEN;
const cesiumIonToken = import.meta.env.VITE_CESIUM_ION_TOKEN;

type EarthMapStore = {
  initTerrainLayer: () => void;
  testTerrainLayer: () => void;
  viewer: any | null;
  objm: ESObjectsManager | null;
  initObjm: () => ESObjectsManager;
  getObjm: () => ESObjectsManager | null;
};

const ImageLayerJsonArr = [
  {
    id: 'basic-map-layer',
    type: 'ESImageryLayer',
    url: `https://t3.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tdtToken}`,
    zIndex: 3,
    name: '天地图影像',
    allowPicking: true,
    maximumLevel: 18,
    czmSubdomains: subdomains,
  },
  {
    id: 'basic-map-layer-0',
    type: 'ESImageryLayer',
    url: `https://t3.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=${tdtToken}`,
    zIndex: 4,
    name: '天地图影像标注',
    allowPicking: true,
    maximumLevel: 18,
    czmSubdomains: subdomains,
  },
];

export const useEarthMapStore = create<EarthMapStore>()(
  subscribeWithSelector<EarthMapStore>((set, get) => ({
    // 初始状态
    viewer: null,
    objm: null,

    /** 初始化objm并注册模块，只执行一次 */
    initObjm: () => {
      if (!_objm) {
        _objm = new ESObjectsManager(ESCesiumViewer);
        // 可在此注册默认图层、场景等

        forEach(ImageLayerJsonArr, item => {
          console.log(_objm?.sceneTree.createSceneObjectTreeItemFromJson(item));
        });

        // 监听视口状态
        _objm.viewerCreatedEvent.don(viewer => {
          console.log(viewer, 'viewer created');

          get().initTerrainLayer();
        });

        set({ objm: _objm });
      }
      return _objm;
    },

    initViewer: (dom: ESVOptionCzm) => {
      const objm = get().getObjm();
      if (!objm) return;

      const viewer = objm!.createCesiumViewer(dom);
      viewer.ionAccessToken = cesiumIonToken;

      set({ viewer });
      return viewer;
    },

    initTerrainLayer: () => {
      const objm = get().getObjm();
      if (!objm) {
        console.error('获取objm失败');
        return;
      }

      try {
        const terrainLayer = objm.getSceneObject('basic-terrain') as ESTerrainLayer;
        if (terrainLayer) {
          return;
        }

        // 添加天地图地形服务
        const terrainLayerJson = objm.createSceneObjectFromJson({
          type: 'ESTerrainLayer',
          id: 'basic-terrain',
          url: 'http://data.mars3d.cn/terrain',
        });

        if (!terrainLayerJson) {
          console.error('地形加载失败！');
          return;
        }

        setTimeout(() => {
          console.log('地形加载完成！');
        }, 1000);
      } catch (error) {
        console.error('地形初始化错误:', error);
      }
    },

    // 屏蔽地形
    disableTerrain: () => {
      const objm = get().getObjm();
      if (!objm) {
        console.error('ObjectsManager未初始化');
        return;
      }

      const terrainLayer = objm.getSceneObject('basic-terrain') as ESTerrainLayer;
      if (terrainLayer) {
        terrainLayer.show = false;
      }
    },

    testTerrainLayer: () => {
      console.log('=== 地形图层诊断信息 ===');
      console.log('天地图个人Token:', tdtToken_terrian ? '已配置' : '未配置');
      console.log('天地图公司Token:', tdtToken ? '已配置' : '未配置');
      console.log('Cesium Ion Token:', cesiumIonToken ? '已配置' : '未配置');

      const objm = get().getObjm();
      if (!objm) {
        console.error('ObjectsManager未初始化');
        return;
      }

      console.log('ObjectsManager状态:', '已初始化');
      console.log('建议的地形服务URL测试:');
      console.log(
        '1. 天地图地形:',
        `https://t0.tianditu.gov.cn/mapservice/swdx?tk=${tdtToken_terrian}`
      );
      console.log('2. Cesium World Terrain:', 'https://assets.agi.com/stk-terrain/world');
      console.log('3. Cesium Ion Terrain (需要token):', 'https://assets.cesium.com/1/');
    },

    /** 获取objm实例 */
    getObjm: () => {
      if (!_objm) {
        get().initObjm();
      }
      return _objm;
    },

    set,
  }))
);
