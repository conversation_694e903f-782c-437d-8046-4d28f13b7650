import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { appRoutes, AppRouteConfig } from './config';

/**
 * 路由工具 Hook
 */
export const useRouter = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const params = useParams();

  return {
    location,
    navigate,
    params,
    // 便捷的导航方法
    push: (path: string) => navigate(path),
    replace: (path: string) => navigate(path, { replace: true }),
    back: () => navigate(-1),
    forward: () => navigate(1),
  };
};

/**
 * 获取当前路由信息
 */
export const useCurrentRoute = (): AppRouteConfig | undefined => {
  const location = useLocation();
  return appRoutes.find(route => route.path === location.pathname);
};

/**
 * 检查路由是否激活
 */
export const useIsRouteActive = () => {
  const location = useLocation();

  return (path: string) => {
    if (path === '/') {
      return location.pathname === '/';
    }
    return location.pathname.startsWith(path);
  };
};

/**
 * 路由历史管理
 */
export const useRouteHistory = () => {
  const navigate = useNavigate();

  // 可以在这里添加路由历史记录的逻辑
  const history: string[] = [];

  const addToHistory = (path: string) => {
    if (history[history.length - 1] !== path) {
      history.push(path);
    }
  };

  const goToLastRoute = () => {
    if (history.length > 1) {
      const lastRoute = history[history.length - 2];
      navigate(lastRoute);
    }
  };

  return {
    history,
    addToHistory,
    goToLastRoute,
  };
};

/**
 * 路由元数据工具
 */
export const getRouteMetadata = (path: string) => {
  const route = appRoutes.find(r => r.path === path);

  return {
    title: route?.label || '页面',
    description: route?.description || '',
    icon: route?.icon || '📄',
  };
};

/**
 * 生成页面标题
 */
export const usePageTitle = (suffix: string = 'Tauri App') => {
  const location = useLocation();
  const route = appRoutes.find(r => r.path === location.pathname);

  return route ? `${route.label} - ${suffix}` : suffix;
};
