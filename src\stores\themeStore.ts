import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { createTheme, Theme as MuiTheme, ThemeOptions } from '@mui/material/styles';
import { Theme, UserPreferences } from './types';

interface ThemeState {
  theme: Theme;
  preferences: UserPreferences;
  muiTheme: MuiTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  resetPreferences: () => void;
}

// 默认用户偏好设置
const defaultPreferences: UserPreferences = {
  theme: 'light',
  language: 'zh-CN',
  notifications: true,
  autoSave: true,
  compactMode: true,
};

// 创建 MUI 主题配置
const createMuiTheme = (mode: 'light' | 'dark', compactMode: boolean = false): MuiTheme => {
  const themeOptions: ThemeOptions = {
    palette: {
      mode,
      primary: {
        main: mode === 'light' ? '#1976d2' : '#90caf9',
      },
      secondary: {
        main: mode === 'light' ? '#dc004e' : '#f48fb1',
      },
      background: {
        default: mode === 'light' ? '#fafafa' : '#121212',
        paper: mode === 'light' ? '#ffffff' : '#1e1e1e',
      },
    },
    typography: {
      fontFamily: [
        'Roboto',
        '-apple-system',
        'BlinkMacSystemFont',
        '"Segoe UI"',
        '"Helvetica Neue"',
        'Arial',
        'sans-serif',
      ].join(','),
    },
    spacing: compactMode ? 6 : 8,
    components: {
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: mode === 'light' ? '#1976d2' : '#1e1e1e',
          },
        },
      },
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            borderRadius: compactMode ? 4 : 8,
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: compactMode ? 4 : 12,
            boxShadow: mode === 'light' ? '0 2px 8px rgba(0,0,0,0.1)' : '0 2px 8px rgba(0,0,0,0.3)',
          },
        },
      },
    },
  };

  return createTheme(themeOptions);
};

/**
 * 主题状态管理 (整合版)
 * 管理应用主题、用户偏好设置和 MUI 主题系统
 */
export const useThemeStore = create<ThemeState>()(
  persist(
    (set, get) => ({
      theme: 'light',
      preferences: defaultPreferences,
      muiTheme: createMuiTheme('light', true),

      setTheme: (theme: Theme) => {
        const currentPreferences = get().preferences;
        const effectiveMode =
          theme === 'auto'
            ? window.matchMedia('(prefers-color-scheme: dark)').matches
              ? 'dark'
              : 'light'
            : theme;

        const newMuiTheme = createMuiTheme(
          effectiveMode as 'light' | 'dark',
          currentPreferences.compactMode
        );

        set(state => ({
          theme,
          preferences: {
            ...state.preferences,
            theme,
          },
          muiTheme: newMuiTheme,
        }));
      },

      toggleTheme: () => {
        const currentTheme = get().theme;
        let newTheme: Theme;

        switch (currentTheme) {
          case 'light':
            newTheme = 'dark';
            break;
          case 'dark':
            newTheme = 'auto';
            break;
          default:
            newTheme = 'light';
            break;
        }

        get().setTheme(newTheme);
      },

      updatePreferences: (newPreferences: Partial<UserPreferences>) => {
        const currentState = get();
        const updatedPreferences = {
          ...currentState.preferences,
          ...newPreferences,
        };

        const effectiveTheme =
          updatedPreferences.theme === 'auto'
            ? window.matchMedia('(prefers-color-scheme: dark)').matches
              ? 'dark'
              : 'light'
            : updatedPreferences.theme;

        const newMuiTheme = createMuiTheme(
          effectiveTheme as 'light' | 'dark',
          updatedPreferences.compactMode
        );

        set({
          preferences: updatedPreferences,
          theme: newPreferences.theme || currentState.theme,
          muiTheme: newMuiTheme,
        });
      },

      resetPreferences: () => {
        const newMuiTheme = createMuiTheme('light', false);
        set({
          preferences: defaultPreferences,
          theme: defaultPreferences.theme,
          muiTheme: newMuiTheme,
        });
      },
    }),
    {
      name: 'theme-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        theme: state.theme,
        preferences: state.preferences,
      }),
    }
  )
);

// 监听系统主题变化
if (typeof window !== 'undefined') {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  mediaQuery.addEventListener('change', () => {
    const store = useThemeStore.getState();
    if (store.theme === 'auto') {
      store.setTheme('auto'); // 重新计算主题
    }
  });
}
