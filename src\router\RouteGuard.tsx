import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';

interface RouteGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  redirectTo?: string;
}

/**
 * 路由守卫组件
 * 用于保护需要权限的路由
 */
const RouteGuard: React.FC<RouteGuardProps> = ({
  children,
  requireAuth = false,
  redirectTo = '/',
}) => {
  const location = useLocation();

  // 这里可以添加权限检查逻辑
  const isAuthenticated = true; // 示例：始终返回true

  if (requireAuth && !isAuthenticated) {
    // 重定向到登录页面，并保存当前路径
    return (
      <Navigate
        to={redirectTo}
        state={{ from: location }}
        replace
      />
    );
  }

  return <>{children}</>;
};

export default RouteGuard;
