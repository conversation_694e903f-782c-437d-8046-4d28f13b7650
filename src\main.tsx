import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import App from '@/App';
import { useThemeStore } from '@/stores';
import 'virtual:uno.css';
import '@/styles/index.less'; // 全局样式

// MUI 主题提供者组件
const MuiThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const muiTheme = useThemeStore(state => state.muiTheme);

  return (
    <ThemeProvider theme={muiTheme}>
      <CssBaseline />
      {children}
    </ThemeProvider>
  );
};

createRoot(document.getElementById('root') as HTMLElement).render(
  <StrictMode>
    <MuiThemeProvider>
      <App />
    </MuiThemeProvider>
  </StrictMode>
);
