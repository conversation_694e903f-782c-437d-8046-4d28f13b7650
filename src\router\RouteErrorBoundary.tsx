import React from 'react';
import { useRouteError, isRouteErrorResponse, Link } from 'react-router-dom';

/**
 * 路由错误边界组件
 * 处理路由加载和渲染错误
 */
const RouteErrorBoundary: React.FC = () => {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-8">
        <div className="text-center max-w-md">
          <div className="mb-8">
            <h1 className="text-6xl font-bold text-red-500 mb-4">{error.status}</h1>
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              {error.status === 404 ? '页面未找到' : '出现错误'}
            </h2>
            <p className="text-lg text-gray-600 mb-6">
              {error.statusText || error.data?.message || '抱歉，发生了意外错误'}
            </p>
          </div>

          <div className="mb-8">
            <div className="w-32 h-32 mx-auto bg-gradient-to-r from-red-400 to-orange-500 rounded-full flex items-center justify-center">
              <span className="text-4xl text-white">😞</span>
            </div>
          </div>

          <div className="space-y-4">
            <Link
              to="/"
              className="btn-primary"
            >
              🏠 返回首页
            </Link>
            <button
              onClick={() => window.location.reload()}
              className="btn-secondary ml-4"
            >
              🔄 刷新页面
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 处理其他类型的错误
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-8">
      <div className="text-center max-w-md">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-red-500 mb-4">应用错误</h1>
          <p className="text-lg text-gray-600 mb-6">应用程序遇到了意外错误，请稍后重试。</p>
        </div>

        <div className="mb-8">
          <div className="w-32 h-32 mx-auto bg-gradient-to-r from-red-400 to-orange-500 rounded-full flex items-center justify-center">
            <span className="text-4xl text-white">⚠️</span>
          </div>
        </div>

        {process.env.NODE_ENV === 'development' && (
          <details className="text-left mb-6 p-4 bg-white rounded-lg shadow">
            <summary className="cursor-pointer font-medium text-gray-800 mb-2">
              错误详情 (开发模式)
            </summary>
            <pre className="text-sm text-red-600 overflow-auto">
              {error instanceof Error ? error.stack : String(error)}
            </pre>
          </details>
        )}

        <div className="space-y-4">
          <Link
            to="/"
            className="btn-primary"
          >
            🏠 返回首页
          </Link>
          <button
            onClick={() => window.location.reload()}
            className="btn-secondary ml-4"
          >
            🔄 刷新页面
          </button>
        </div>
      </div>
    </div>
  );
};

export default RouteErrorBoundary;
