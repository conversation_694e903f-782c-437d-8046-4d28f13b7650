import { JSX, useEffect, useRef } from 'react';
import './style.less';
import { Container } from '../widigets';
import { ES3DTileset, ESObjectsManager } from 'earthsdk3';

import { useEarthMapStore } from '@/stores/earthMapStore';

export default function MapContainer(): JSX.Element {
  const containerRef = useRef<HTMLDivElement | null>(null);
  const viewer = useRef<any>(null);
  const objm = useRef<ESObjectsManager>(null);

  // 获取store方法
  const { initObjm, initViewer }: any = useEarthMapStore();

  // 初始化objm和Cesium
  useEffect(() => {
    if (!containerRef.current) return;

    objm.current = initObjm();

    if (!objm.current) return;

    viewer.current = initViewer(containerRef.current);
    console.log('viewer', viewer.current);
  }, []);

  useEffect(() => {
    if (objm.current) {
      const sceneObject = objm.current.createSceneObject(ES3DTileset);

      // url服务地址
      sceneObject!.url = 'http://192.168.101.115:81/hqmodel/floor-4/tileset.json'; // 'https://www.earthsdk.com/assets/osgb_dayanta/tileset.json';
      // 加载完成事件
      sceneObject!.tilesetReady.don(() => {
        sceneObject!.flyTo();
        sceneObject!.allowPicking = true; // 允许拾取
      });
    }
  }, [objm.current]);

  return (
    <div className="box">
      <Container viewer={viewer.current} />
      <div
        className="w-full h-full "
        ref={containerRef}
      ></div>
    </div>
  );
}
