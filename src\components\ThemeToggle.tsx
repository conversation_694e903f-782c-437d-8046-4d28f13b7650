import React from 'react';
import { IconButton, Tooltip, useTheme } from '@mui/material';
import { Brightness4, Brightness7, SettingsBrightness } from '@mui/icons-material';
import { useThemeStore } from '../stores';

/**
 * 主题切换组件 - 整合 MUI 设计
 */
const ThemeToggle: React.FC = () => {
  const muiTheme = useTheme();
  const { theme, toggleTheme } = useThemeStore();

  const getThemeIcon = () => {
    switch (theme) {
      case 'light':
        return <Brightness7 />;
      case 'dark':
        return <Brightness4 />;
      case 'auto':
        return <SettingsBrightness />;
      default:
        return <Brightness7 />;
    }
  };

  const getThemeLabel = () => {
    switch (theme) {
      case 'light':
        return '浅色模式';
      case 'dark':
        return '深色模式';
      case 'auto':
        return '自动模式 (跟随系统)';
      default:
        return '浅色模式';
    }
  };

  return (
    <Tooltip
      title={`当前: ${getThemeLabel()}, 点击切换`}
      arrow
    >
      <IconButton
        onClick={toggleTheme}
        color="inherit"
        sx={{
          color: muiTheme.palette.text.primary,
          '&:hover': {
            backgroundColor: muiTheme.palette.action.hover,
          },
        }}
      >
        {getThemeIcon()}
      </IconButton>
    </Tooltip>
  );
};

export default ThemeToggle;
