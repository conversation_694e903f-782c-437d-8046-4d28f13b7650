import { RouteObject } from 'react-router-dom';
import RouteErrorBoundary from '@/router/RouteErrorBoundary';
import { ViewMap } from '@/pages';

/**
 * 路由配置接口
 */
export interface AppRouteConfig {
  path: string;
  label: string;
  icon: string;
  description?: string;
  showInNav?: boolean;
  element?: React.ComponentType;
  children?: SubRouteConfig[];
}

/**
 * 子路由配置接口
 */
export interface SubRouteConfig {
  path: string;
  label: string;
  icon: string;
  description?: string;
  element?: React.ComponentType;
}

/**
 * 应用路由配置
 */
export const appRoutes: AppRouteConfig[] = [];

/**
 * React Router 路由配置
 */
export const routerConfig: RouteObject[] = [
  {
    path: '/',
    element: <ViewMap />,
    errorElement: <RouteErrorBoundary />,
  },
  // {
  //   path: '/',
  //   element: <Layout />,
  //   errorElement: <RouteErrorBoundary />,
  //   children: [
  //     {
  //       index: true,
  //       element: <Dashboard />,
  //     },
  //     {
  //       path: 'users',
  //       element: <Users />,
  //     },
  //     {
  //       path: 'cesium-demo',
  //       element: <CesiumMapSimple />,
  //     },
  //     {
  //       path: 'cesium-diagnostics',
  //       element: <CesiumDiagnostics />,
  //     },
  //     {
  //       path: 'settings',
  //       element: <Settings />,
  //     },
  //     {
  //       path: '*',
  //       element: <NotFound />,
  //     },
  //   ],
  // },
];

/**
 * 获取导航栏显示的路由
 */
export const getNavRoutes = (): AppRouteConfig[] => {
  return appRoutes.filter(route => route.showInNav);
};
