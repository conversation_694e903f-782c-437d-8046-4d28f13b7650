/**
 * 状态管理类型定义
 */

// 主题类型 - 支持自动跟随系统主题
export type Theme = 'light' | 'dark' | 'auto';

// 用户信息类型
export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  preferences: UserPreferences;
}

// 用户偏好设置
export interface UserPreferences {
  theme: Theme;
  language: 'zh-CN' | 'en-US';
  notifications: boolean;
  autoSave: boolean;
  compactMode: boolean;
}

// 应用设置类型
export interface AppSettings {
  windowSize: {
    width: number;
    height: number;
  };
  windowPosition: {
    x: number;
    y: number;
  };
  alwaysOnTop: boolean;
  minimizeToTray: boolean;
  startMinimized: boolean;
  autoStart: boolean;
}

// 应用状态类型
export interface AppState {
  isLoading: boolean;
  error: string | null;
  currentRoute: string;
  sidebarCollapsed: boolean;
  fullscreen: boolean;
}

// 通知类型
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}
