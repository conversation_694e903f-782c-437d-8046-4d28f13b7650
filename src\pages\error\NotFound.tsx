import React from 'react';
import { Link } from 'react-router-dom';
import { Box, Typography, Button, Stack, Container, Avatar } from '@mui/material';
import { Home, ArrowBack } from '@mui/icons-material';

const NotFound: React.FC = () => {
  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #ffebee 0%, #fce4ec 100%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 4,
      }}
    >
      <Container maxWidth="md">
        <Box sx={{ textAlign: 'center' }}>
          {/* 404 标题 */}
          <Typography
            variant="h1"
            component="h1"
            sx={{
              fontSize: { xs: '6rem', md: '8rem' },
              fontWeight: 'bold',
              color: 'error.main',
              mb: 2,
            }}
          >
            404
          </Typography>

          <Typography
            variant="h3"
            component="h2"
            gutterBottom
            sx={{ fontWeight: 'bold' }}
          >
            页面未找到
          </Typography>

          <Typography
            variant="h6"
            color="text.secondary"
            paragraph
            sx={{ mb: 4 }}
          >
            抱歉，您访问的页面不存在或已被移动。
          </Typography>

          {/* 错误图标 */}
          <Avatar
            sx={{
              width: 200,
              height: 200,
              mx: 'auto',
              mb: 4,
              background: 'linear-gradient(135deg, #f44336 0%, #ff9800 100%)',
              fontSize: '6rem',
            }}
          >
            😕
          </Avatar>

          {/* 操作按钮 */}
          <Stack
            direction={{ xs: 'column', sm: 'row' }}
            spacing={2}
            justifyContent="center"
            sx={{ mb: 4 }}
          >
            <Button
              component={Link}
              to="/"
              variant="contained"
              startIcon={<Home />}
              size="large"
            >
              返回首页
            </Button>
            <Button
              variant="outlined"
              startIcon={<ArrowBack />}
              onClick={() => window.history.back()}
              size="large"
            >
              返回上一页
            </Button>
          </Stack>
        </Box>
      </Container>
    </Box>
  );
};

export default NotFound;
