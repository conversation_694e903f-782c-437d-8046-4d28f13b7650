import { createBrowserRouter, RouterProvider } from 'react-router-dom';
import { routerConfig } from './config';

/**
 * 创建路由实例
 */
export const router = createBrowserRouter(routerConfig);

/**
 * 路由提供者组件
 */
export const AppRouter: React.FC = () => {
  return <RouterProvider router={router} />;
};

// 导出路由配置相关
export * from './config';
export { default as RouteGuard } from './RouteGuard';
export { default as RouteErrorBoundary } from './RouteErrorBoundary';
