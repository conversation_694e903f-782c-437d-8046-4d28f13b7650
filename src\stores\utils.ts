import { useEffect } from 'react';
import { useAppStore } from './appStore';
import { useThemeStore } from './themeStore';
import { useUserStore } from './userStore';
import { useAppSettingsStore } from './appSettingsStore';
import { useNotificationStore } from './notificationStore';

/**
 * 状态管理工具函数
 */

/**
 * 同步路由变化到状态管理
 */
export const useSyncRoute = (pathname: string) => {
  const setCurrentRoute = useAppStore(state => state.setCurrentRoute);

  useEffect(() => {
    setCurrentRoute(pathname);
  }, [pathname, setCurrentRoute]);
};

/**
 * 主题同步 Hook
 * 自动应用主题到 document 元素
 */
export const useThemeSync = () => {
  const theme = useThemeStore(state => state.theme);

  useEffect(() => {
    const root = document.documentElement;

    // 移除所有主题类
    root.classList.remove('light', 'dark');

    // 添加当前主题类
    if (theme === 'auto') {
      // 检测系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.classList.add(prefersDark ? 'dark' : 'light');
    } else {
      root.classList.add(theme);
    }
  }, [theme]);

  return theme;
};

/**
 * 获取所有状态的调试信息
 */
export const getStoreDebugInfo = () => {
  const app = useAppStore.getState();
  const theme = useThemeStore.getState();
  const user = useUserStore.getState();
  const settings = useAppSettingsStore.getState();
  const notifications = useNotificationStore.getState();

  return {
    app,
    theme,
    user,
    settings,
    notifications,
  };
};

/**
 * 重置所有状态到默认值
 */
export const resetAllStores = () => {
  // 重置主题
  useThemeStore.getState().resetPreferences();

  // 重置应用设置
  useAppSettingsStore.getState().resetSettings();

  // 登出用户
  useUserStore.getState().logout();

  // 清空通知
  useNotificationStore.getState().clearNotifications();

  // 重置应用状态
  const appStore = useAppStore.getState();
  appStore.setLoading(false);
  appStore.setError(null);
  appStore.setSidebarCollapsed(false);
  appStore.setFullscreen(false);
};

/**
 * 批量操作工具
 */
export const batchActions = {
  /**
   * 显示加载状态并执行异步操作
   */
  withLoading: async <T>(action: () => Promise<T>): Promise<T> => {
    const { setLoading, setError } = useAppStore.getState();

    try {
      setLoading(true);
      setError(null);
      const result = await action();
      return result;
    } catch (error) {
      setError(error instanceof Error ? error.message : '操作失败');
      throw error;
    } finally {
      setLoading(false);
    }
  },

  /**
   * 显示成功通知
   */
  showSuccess: (message: string) => {
    useNotificationStore.getState().addNotification({
      type: 'success',
      title: '操作成功',
      message,
    });
  },

  /**
   * 显示错误通知
   */
  showError: (message: string) => {
    useNotificationStore.getState().addNotification({
      type: 'error',
      title: '操作失败',
      message,
    });
  },
};
