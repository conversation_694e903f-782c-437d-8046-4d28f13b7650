@import '@fontsource/roboto/300.css';
@import '@fontsource/roboto/400.css';
@import '@fontsource/roboto/500.css';
@import '@fontsource/roboto/700.css';

/* 基础重置样式 */
* {
  box-sizing: border-box;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  overflow-x: hidden !important;
  margin: 0;
  padding: 0;
  font-family:
    'Roboto',
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    sans-serif;
}

#root {
  width: 100%;
  height: 100%;
}

/* UnoCSS 工具类增强 */
@layer utilities {
  /* 自定义滚动条 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(155, 155, 155, 0.5) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(155, 155, 155, 0.5);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(155, 155, 155, 0.7);
  }

  /* 过渡动画类 */
  .transition-all {
    transition: all 0.3s ease;
  }

  /* 阴影工具类 */
  .shadow-soft {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  .shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }

  .shadow-strong {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  }

  /* 渐变背景 */
  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }
}

/* 深色模式支持 */
:root {
  --color-primary: #3b82f6;
  --color-primary-dark: #1d4ed8;
  --color-bg: #ffffff;
  --color-bg-secondary: #f9fafb;
  --color-text: #1f2937;
  --color-text-secondary: #6b7280;
  --color-border: #d1d5db;
}

.dark {
  --color-primary: #60a5fa;
  --color-primary-dark: #3b82f6;
  --color-bg: #111827;
  --color-bg-secondary: #1f2937;
  --color-text: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-border: #374151;
}

/* 全局深色模式样式 */
.dark {
  color-scheme: dark;
}
