{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-app", "version": "0.1.0", "identifier": "com.tauri-app.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "plugins": {}, "app": {"windows": [{"title": "一个小小的Tauri", "width": 1200, "height": 800, "minWidth": 600, "minHeight": 400, "resizable": true, "center": true, "decorations": true, "alwaysOnTop": false, "fullscreen": false, "transparent": false, "maximized": false, "visible": true, "skipTaskbar": false, "theme": "Light"}], "security": {"csp": {"default-src": ["'self'"], "script-src": ["'self'", "'unsafe-inline'", "'unsafe-eval'"], "style-src": ["'self'", "'unsafe-inline'"], "img-src": ["'self'", "data:", "https://*.tianditu.gov.cn", "https://server.arcgisonline.com", "https://services.arcgisonline.com", "https://*.amap.com", "https://*.baidu.com", "https://tiles.example.com"], "font-src": ["'self'", "data:"], "frame-src": ["'self'", "data:", "blob:"], "worker-src": ["'self'", "blob:"], "connect-src": ["'self'", "https://*.tianditu.gov.cn", "https://server.arcgisonline.com", "https://services.arcgisonline.com", "https://*.amap.com", "https://*.baidu.com", "https://tiles.example.com"]}}, "macOSPrivateApi": false, "withGlobalTauri": false}, "bundle": {"active": true, "targets": ["msi", "nsis"], "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "publisher": "您的公司名称", "category": "Productivity", "shortDescription": "基于 Tauri 构建的现代桌面应用程序", "longDescription": "一个功能强大且高效的桌面应用程序，基于 Tauri 框架构建，结合了 Web 技术的最佳特性与原生性能。", "windows": {"webviewInstallMode": {"type": "downloadBootstrapper"}, "nsis": {"installMode": "perMachine"}, "wix": {"language": "zh-CN"}}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13"}, "linux": {"deb": {"depends": []}, "appimage": {"bundleMediaFramework": false}}}}